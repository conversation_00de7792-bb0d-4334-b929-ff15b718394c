# nova_windows.py
import os
import subprocess
import psutil
import pyautogui
from datetime import datetime
from config import nova_config as conf

# Windows-specific imports
try:
    from pycaw.pycaw import AudioUtilities, IAudioEndpointVolume
    from comtypes import CLSCTX_ALL
    import comtypes
    AUDIO_AVAILABLE = True
except ImportError:
    AUDIO_AVAILABLE = False

class WindowsController:
    def __init__(self):
        self.screenshot_path = conf.SCREENSHOT_PATH
        if not os.path.exists(self.screenshot_path):
            os.makedirs(self.screenshot_path)
        
        # Initialize audio control
        if AUDIO_AVAILABLE:
            try:
                devices = AudioUtilities.GetSpeakers()
                interface = devices.Activate(IAudioEndpointVolume._iid_, CLSCTX_ALL, None)
                self.volume = interface.QueryInterface(IAudioEndpointVolume)
            except Exception:
                self.volume = None
        else:
            self.volume = None
    
    def set_volume(self, level):
        """Set system volume (0-100)"""
        try:
            if not self.volume:
                return "🔊 Audio control not available on this system."
            
            # Clamp level between 0 and 100
            level = max(0, min(100, int(level)))
            
            # Convert to scalar (0.0 to 1.0)
            scalar_level = level / 100.0
            
            self.volume.SetMasterScalarVolume(scalar_level, None)
            
            # Get emoji based on volume level
            if level == 0:
                emoji = "🔇"
            elif level < 30:
                emoji = "🔈"
            elif level < 70:
                emoji = "🔉"
            else:
                emoji = "🔊"
            
            return f"{emoji} **Volume set to {level}%**\n\nPerfect! Audio level adjusted. 🎵"
            
        except Exception as e:
            return f"🔊 Sorry, couldn't adjust volume: {str(e)}"
    
    def get_volume(self):
        """Get current system volume"""
        try:
            if not self.volume:
                return "🔊 Audio control not available on this system."
            
            current_volume = int(self.volume.GetMasterScalarVolume() * 100)
            
            if current_volume == 0:
                emoji = "🔇"
                status = "Muted"
            elif current_volume < 30:
                emoji = "🔈"
                status = "Low"
            elif current_volume < 70:
                emoji = "🔉"
                status = "Medium"
            else:
                emoji = "🔊"
                status = "High"
            
            return f"{emoji} **Current Volume: {current_volume}%**\n\n📊 Status: {status}\n\nNeed me to adjust it? 🎛️"
            
        except Exception as e:
            return f"🔊 Sorry, couldn't get volume: {str(e)}"
    
    def mute_volume(self):
        """Mute system volume"""
        try:
            if not self.volume:
                return "🔊 Audio control not available on this system."
            
            self.volume.SetMute(1, None)
            return "🔇 **Audio Muted**\n\nSilence achieved! Let me know when you want sound back. 🤫"
            
        except Exception as e:
            return f"🔊 Sorry, couldn't mute: {str(e)}"
    
    def unmute_volume(self):
        """Unmute system volume"""
        try:
            if not self.volume:
                return "🔊 Audio control not available on this system."
            
            self.volume.SetMute(0, None)
            current_volume = int(self.volume.GetMasterScalarVolume() * 100)
            return f"🔊 **Audio Unmuted**\n\n📊 Volume: {current_volume}%\nWelcome back to the world of sound! 🎵"
            
        except Exception as e:
            return f"🔊 Sorry, couldn't unmute: {str(e)}"
    
    def open_application(self, app_name):
        """Open an application"""
        try:
            # Common application mappings
            app_mappings = {
                'notepad': 'notepad.exe',
                'calculator': 'calc.exe',
                'paint': 'mspaint.exe',
                'chrome': 'chrome.exe',
                'firefox': 'firefox.exe',
                'edge': 'msedge.exe',
                'explorer': 'explorer.exe',
                'cmd': 'cmd.exe',
                'powershell': 'powershell.exe',
                'task manager': 'taskmgr.exe',
                'control panel': 'control.exe',
                'settings': 'ms-settings:',
                'word': 'winword.exe',
                'excel': 'excel.exe',
                'powerpoint': 'powerpnt.exe',
                'outlook': 'outlook.exe',
                'teams': 'teams.exe',
                'discord': 'discord.exe',
                'spotify': 'spotify.exe',
                'steam': 'steam.exe'
            }
            
            app_lower = app_name.lower()
            
            if app_lower in app_mappings:
                executable = app_mappings[app_lower]
                
                if executable.startswith('ms-settings:'):
                    # Special case for Windows Settings
                    os.system(f'start {executable}')
                else:
                    subprocess.Popen(executable, shell=True)
                
                return f"🚀 **{app_name.title()} Launched!**\n\nOpening {app_name} for you now. ✨"
            else:
                # Try to open directly
                subprocess.Popen(app_name, shell=True)
                return f"🚀 **Attempting to launch {app_name}**\n\nTrying to open that for you! 🎯"
                
        except Exception as e:
            return f"❌ Sorry, couldn't open {app_name}: {str(e)}"
    
    def close_application(self, app_name):
        """Close an application"""
        try:
            closed_count = 0
            
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if app_name.lower() in proc.info['name'].lower():
                        proc.terminate()
                        closed_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if closed_count > 0:
                return f"✅ **{app_name.title()} Closed**\n\nClosed {closed_count} instance{'s' if closed_count != 1 else ''} of {app_name}. 🎯"
            else:
                return f"🔍 **{app_name.title()} Not Found**\n\nCouldn't find any running instances of {app_name}. 🤔"
                
        except Exception as e:
            return f"❌ Sorry, couldn't close {app_name}: {str(e)}"
    
    def list_running_apps(self):
        """List currently running applications"""
        try:
            processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
                try:
                    # Filter out system processes and focus on user applications
                    if (proc.info['name'].endswith('.exe') and 
                        not proc.info['name'].startswith('svchost') and
                        not proc.info['name'].startswith('System')):
                        
                        memory_mb = proc.info['memory_info'].rss / 1024 / 1024
                        processes.append({
                            'name': proc.info['name'],
                            'pid': proc.info['pid'],
                            'memory': memory_mb
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # Sort by memory usage
            processes.sort(key=lambda x: x['memory'], reverse=True)
            
            response = "🖥️ **Running Applications**\n\n"
            
            # Show top 10 applications
            for i, proc in enumerate(processes[:10], 1):
                app_name = proc['name'].replace('.exe', '').title()
                memory_str = f"{proc['memory']:.1f} MB"
                response += f"**{i}.** {app_name} (PID: {proc['pid']}) - {memory_str}\n"
            
            if len(processes) > 10:
                response += f"\n... and {len(processes) - 10} more applications running."
            
            response += "\n\nNeed me to close any of these? 🎛️"
            return response
            
        except Exception as e:
            return f"🖥️ Sorry, couldn't list applications: {str(e)}"
    
    def take_screenshot(self):
        """Take a screenshot"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"
            filepath = os.path.join(self.screenshot_path, filename)
            
            screenshot = pyautogui.screenshot()
            screenshot.save(filepath)
            
            return f"📸 **Screenshot Captured!**\n\n💾 **Saved as**: {filename}\n📁 **Location**: {self.screenshot_path}\n\nPerfect shot! 📷✨"
            
        except Exception as e:
            return f"📸 Sorry, couldn't take screenshot: {str(e)}"
    
    def lock_computer(self):
        """Lock the computer"""
        try:
            os.system("rundll32.exe user32.dll,LockWorkStation")
            return "🔒 **Computer Locked**\n\nSecurity engaged! See you when you get back. 🛡️"
        except Exception as e:
            return f"🔒 Sorry, couldn't lock computer: {str(e)}"
    
    def shutdown_computer(self, delay_minutes=1):
        """Shutdown computer with delay"""
        try:
            delay_seconds = delay_minutes * 60
            os.system(f"shutdown /s /t {delay_seconds}")
            return f"🔌 **Shutdown Scheduled**\n\n⏰ Computer will shutdown in {delay_minutes} minute{'s' if delay_minutes != 1 else ''}.\n\nUse 'shutdown /a' in command prompt to cancel. 💤"
        except Exception as e:
            return f"🔌 Sorry, couldn't schedule shutdown: {str(e)}"
    
    def restart_computer(self, delay_minutes=1):
        """Restart computer with delay"""
        try:
            delay_seconds = delay_minutes * 60
            os.system(f"shutdown /r /t {delay_seconds}")
            return f"🔄 **Restart Scheduled**\n\n⏰ Computer will restart in {delay_minutes} minute{'s' if delay_minutes != 1 else ''}.\n\nUse 'shutdown /a' in command prompt to cancel. 🔄"
        except Exception as e:
            return f"🔄 Sorry, couldn't schedule restart: {str(e)}"

# Global Windows controller instance
windows_controller = WindowsController()

# Main functions for Nova to use
def set_volume(level):
    """Set volume - main function for Nova to use"""
    return windows_controller.set_volume(level)

def get_volume():
    """Get volume - main function for Nova to use"""
    return windows_controller.get_volume()

def mute_volume():
    """Mute volume - main function for Nova to use"""
    return windows_controller.mute_volume()

def unmute_volume():
    """Unmute volume - main function for Nova to use"""
    return windows_controller.unmute_volume()

def open_application(app_name):
    """Open application - main function for Nova to use"""
    return windows_controller.open_application(app_name)

def close_application(app_name):
    """Close application - main function for Nova to use"""
    return windows_controller.close_application(app_name)

def list_running_apps():
    """List running apps - main function for Nova to use"""
    return windows_controller.list_running_apps()

def take_screenshot():
    """Take screenshot - main function for Nova to use"""
    return windows_controller.take_screenshot()

def lock_computer():
    """Lock computer - main function for Nova to use"""
    return windows_controller.lock_computer()

def shutdown_computer(delay_minutes=1):
    """Shutdown computer - main function for Nova to use"""
    return windows_controller.shutdown_computer(delay_minutes)

def restart_computer(delay_minutes=1):
    """Restart computer - main function for Nova to use"""
    return windows_controller.restart_computer(delay_minutes)
