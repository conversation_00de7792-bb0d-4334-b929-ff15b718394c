# nova_datetime.py
from datetime import datetime, timedelta, date
import pytz
from dateutil import parser
import calendar

class DateTimeService:
    def __init__(self):
        self.local_tz = pytz.timezone('UTC')  # Default to UTC, can be configured
        
    def get_current_time(self, timezone=None):
        """Get current time with natural language formatting"""
        try:
            if timezone:
                tz = pytz.timezone(timezone)
                now = datetime.now(tz)
            else:
                now = datetime.now()
            
            # Format time naturally
            hour = now.hour
            minute = now.minute
            
            # Determine time of day
            if 5 <= hour < 12:
                period = "morning"
                greeting = "Good morning"
            elif 12 <= hour < 17:
                period = "afternoon"
                greeting = "Good afternoon"
            elif 17 <= hour < 21:
                period = "evening"
                greeting = "Good evening"
            else:
                period = "night"
                greeting = "Good evening"
            
            # Format the time
            time_str = now.strftime("%I:%M %p").lstrip('0')
            date_str = now.strftime("%A, %B %d, %Y")
            
            response = f"""🕐 **Current Time & Date**

⏰ **Time**: {time_str}
📅 **Date**: {date_str}
🌅 **Period**: {period.title()}

{greeting}, Hector! ✨"""
            
            return response
            
        except Exception as e:
            return f"⏰ Sorry, I had trouble getting the time: {str(e)}"
    
    def get_date_info(self, date_input=None):
        """Get detailed information about a specific date"""
        try:
            if date_input:
                target_date = parser.parse(date_input).date()
            else:
                target_date = date.today()
            
            # Calculate day of year
            day_of_year = target_date.timetuple().tm_yday
            days_in_year = 366 if calendar.isleap(target_date.year) else 365
            
            # Calculate week number
            week_number = target_date.isocalendar()[1]
            
            # Days until/since important dates
            today = date.today()
            days_diff = (target_date - today).days
            
            if days_diff == 0:
                relative = "Today"
            elif days_diff == 1:
                relative = "Tomorrow"
            elif days_diff == -1:
                relative = "Yesterday"
            elif days_diff > 0:
                relative = f"In {days_diff} days"
            else:
                relative = f"{abs(days_diff)} days ago"
            
            # Format response
            response = f"""📅 **Date Information**

📆 **Date**: {target_date.strftime('%A, %B %d, %Y')}
🗓️ **Relative**: {relative}
📊 **Day of Year**: {day_of_year} of {days_in_year}
📈 **Week Number**: {week_number}
🌙 **Day of Week**: {target_date.strftime('%A')}

{self._get_seasonal_info(target_date)}"""
            
            return response
            
        except Exception as e:
            return f"📅 Sorry, I couldn't process that date: {str(e)}"
    
    def time_until(self, target_time):
        """Calculate time until a specific time/date"""
        try:
            target = parser.parse(target_time)
            now = datetime.now()
            
            # If target is in the past today, assume tomorrow
            if target.date() == now.date() and target.time() < now.time():
                target = target + timedelta(days=1)
            
            diff = target - now
            
            if diff.total_seconds() < 0:
                return f"⏰ That time has already passed!"
            
            # Format the difference
            days = diff.days
            hours, remainder = divmod(diff.seconds, 3600)
            minutes, _ = divmod(remainder, 60)
            
            parts = []
            if days > 0:
                parts.append(f"{days} day{'s' if days != 1 else ''}")
            if hours > 0:
                parts.append(f"{hours} hour{'s' if hours != 1 else ''}")
            if minutes > 0:
                parts.append(f"{minutes} minute{'s' if minutes != 1 else ''}")
            
            if not parts:
                time_str = "less than a minute"
            else:
                time_str = ", ".join(parts)
            
            response = f"""⏳ **Time Until {target.strftime('%A, %B %d at %I:%M %p')}**

🕐 **Remaining**: {time_str}
📅 **Target Date**: {target.strftime('%A, %B %d, %Y')}
⏰ **Target Time**: {target.strftime('%I:%M %p')}

I'll help you keep track of it! ⭐"""
            
            return response
            
        except Exception as e:
            return f"⏰ Sorry, I couldn't understand that time format: {str(e)}"
    
    def get_timezone_info(self, timezone_name):
        """Get information about a specific timezone"""
        try:
            tz = pytz.timezone(timezone_name)
            now_tz = datetime.now(tz)
            
            response = f"""🌍 **Timezone: {timezone_name}**

⏰ **Current Time**: {now_tz.strftime('%I:%M %p')}
📅 **Date**: {now_tz.strftime('%A, %B %d, %Y')}
🕐 **UTC Offset**: {now_tz.strftime('%z')}

Perfect for staying connected globally! 🌐"""
            
            return response
            
        except Exception as e:
            return f"🌍 Sorry, I couldn't find timezone '{timezone_name}': {str(e)}"
    
    def _get_seasonal_info(self, target_date):
        """Get seasonal information for a date"""
        month = target_date.month
        day = target_date.day
        
        # Determine season (Northern Hemisphere)
        if (month == 12 and day >= 21) or month in [1, 2] or (month == 3 and day < 20):
            season = "Winter ❄️"
        elif (month == 3 and day >= 20) or month in [4, 5] or (month == 6 and day < 21):
            season = "Spring 🌸"
        elif (month == 6 and day >= 21) or month in [7, 8] or (month == 9 and day < 22):
            season = "Summer ☀️"
        else:
            season = "Autumn 🍂"
        
        return f"🌿 **Season**: {season}"

# Global datetime service instance
datetime_service = DateTimeService()

def get_current_time(timezone=None):
    """Get current time - main function for Nova to use"""
    return datetime_service.get_current_time(timezone)

def get_date_info(date_input=None):
    """Get date information - main function for Nova to use"""
    return datetime_service.get_date_info(date_input)

def time_until(target_time):
    """Calculate time until target - main function for Nova to use"""
    return datetime_service.time_until(target_time)

def get_timezone_info(timezone_name):
    """Get timezone info - main function for Nova to use"""
    return datetime_service.get_timezone_info(timezone_name)
