# telegram_bot.py
from telegram import Update
from telegram.ext import <PERSON><PERSON><PERSON><PERSON>, CommandH<PERSON>ler, MessageHandler, ContextTypes, filters
from nova_core import generate_nova_reply, clear_memory

TELEGRAM_BOT_TOKEN = "**********************************************"

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await update.message.reply_text("Hello! Nova is online.")

async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_input = update.message.text
    try:
        if user_input.lower() == "reset":
            clear_memory()
            await update.message.reply_text("Memory cleared.")
            return

        response = generate_nova_reply(user_input)
        await update.message.reply_text(response)

    except Exception as e:
        await update.message.reply_text(f"Error: {str(e)}")

if __name__ == "__main__":
    app = ApplicationBuilder().token(TELEGRAM_BOT_TOKEN).build()
    app.add_handler(CommandHandler("start", start))
    app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))
    app.run_polling()
