# nova_weather.py
import requests
import json
from datetime import datetime, timedelta
from config.api_keys import OPENWEATHER_API_KEY
from config import nova_config as conf

class WeatherService:
    def __init__(self):
        self.api_key = OPENWEATHER_API_KEY
        self.base_url = "http://api.openweathermap.org/data/2.5"
        
    def get_current_weather(self, city=None):
        """Get current weather for specified city or default city"""
        if not city:
            city = conf.DEFAULT_CITY
            
        if self.api_key == "YOUR_OPENWEATHER_API_KEY_HERE":
            return "🌤️ Weather service needs API key configuration. Please get a free key from openweathermap.org"
            
        try:
            url = f"{self.base_url}/weather"
            params = {
                'q': city,
                'appid': self.api_key,
                'units': conf.WEATHER_UNITS
            }
            
            response = requests.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                return self._format_current_weather(data)
            else:
                return f"🌫️ Couldn't get weather for {city}. Please check the city name."
                
        except Exception as e:
            return f"🌫️ Weather service temporarily unavailable: {str(e)}"
    
    def get_weather_forecast(self, city=None, days=3):
        """Get weather forecast for specified city"""
        if not city:
            city = conf.DEFAULT_CITY
            
        if self.api_key == "YOUR_OPENWEATHER_API_KEY_HERE":
            return "🌤️ Weather service needs API key configuration."
            
        try:
            url = f"{self.base_url}/forecast"
            params = {
                'q': city,
                'appid': self.api_key,
                'units': conf.WEATHER_UNITS,
                'cnt': days * 8  # 8 forecasts per day (every 3 hours)
            }
            
            response = requests.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                return self._format_forecast(data, days)
            else:
                return f"🌫️ Couldn't get forecast for {city}."
                
        except Exception as e:
            return f"🌫️ Forecast service temporarily unavailable: {str(e)}"
    
    def _format_current_weather(self, data):
        """Format current weather data into readable text"""
        city = data['name']
        country = data['sys']['country']
        temp = round(data['main']['temp'])
        feels_like = round(data['main']['feels_like'])
        humidity = data['main']['humidity']
        description = data['weather'][0]['description'].title()
        wind_speed = data['wind']['speed']
        
        # Get appropriate emoji for weather
        weather_id = data['weather'][0]['id']
        emoji = self._get_weather_emoji(weather_id)
        
        unit = "°C" if conf.WEATHER_UNITS == "metric" else "°F"
        wind_unit = "m/s" if conf.WEATHER_UNITS == "metric" else "mph"
        
        weather_text = f"""🌍 **Weather in {city}, {country}**

{emoji} **{description}**
🌡️ Temperature: {temp}{unit} (feels like {feels_like}{unit})
💧 Humidity: {humidity}%
💨 Wind: {wind_speed} {wind_unit}

Perfect weather for whatever you have planned! 🌟"""
        
        return weather_text
    
    def _format_forecast(self, data, days):
        """Format forecast data into readable text"""
        city = data['city']['name']
        forecasts = []
        
        # Group forecasts by day
        daily_forecasts = {}
        for item in data['list'][:days*8]:
            date = datetime.fromtimestamp(item['dt']).date()
            if date not in daily_forecasts:
                daily_forecasts[date] = []
            daily_forecasts[date].append(item)
        
        forecast_text = f"📅 **{days}-Day Forecast for {city}**\n\n"
        
        for date, day_data in list(daily_forecasts.items())[:days]:
            # Get midday forecast for the day
            midday_forecast = day_data[len(day_data)//2]
            temp = round(midday_forecast['main']['temp'])
            description = midday_forecast['weather'][0]['description'].title()
            emoji = self._get_weather_emoji(midday_forecast['weather'][0]['id'])
            
            day_name = date.strftime("%A, %B %d")
            unit = "°C" if conf.WEATHER_UNITS == "metric" else "°F"
            
            forecast_text += f"{emoji} **{day_name}**: {temp}{unit}, {description}\n"
        
        forecast_text += "\nStay prepared and have a wonderful time! ✨"
        return forecast_text
    
    def _get_weather_emoji(self, weather_id):
        """Get appropriate emoji for weather condition"""
        if 200 <= weather_id <= 232:  # Thunderstorm
            return "⛈️"
        elif 300 <= weather_id <= 321:  # Drizzle
            return "🌦️"
        elif 500 <= weather_id <= 531:  # Rain
            return "🌧️"
        elif 600 <= weather_id <= 622:  # Snow
            return "❄️"
        elif 701 <= weather_id <= 781:  # Atmosphere (fog, etc.)
            return "🌫️"
        elif weather_id == 800:  # Clear sky
            return "☀️"
        elif 801 <= weather_id <= 804:  # Clouds
            return "☁️"
        else:
            return "🌤️"

# Global weather service instance
weather_service = WeatherService()

def get_weather(city=None):
    """Get current weather - main function for Nova to use"""
    return weather_service.get_current_weather(city)

def get_forecast(city=None, days=3):
    """Get weather forecast - main function for Nova to use"""
    return weather_service.get_weather_forecast(city, days)
