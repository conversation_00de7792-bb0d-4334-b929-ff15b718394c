# nova_logger.py
import os
from datetime import datetime

class NovaLogger:
    def __init__(self):
        self.log_dir = "logs"
        self.ensure_log_directory()
        self.log_file = os.path.join(self.log_dir, f"nova_{datetime.now().strftime('%Y%m%d')}.log")
        self.session_start()

    def ensure_log_directory(self):
        """Create logs directory if it doesn't exist"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)

    def _write_log(self, level, category, message):
        """Write log entry to file"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {level} - {category}: {message}\n"

        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
        except Exception as e:
            print(f"Logging error: {e}")

    def session_start(self):
        """Log session start"""
        self._write_log("INFO", "SESSION", "Nova AI Agent session started")
        self._write_log("INFO", "SESSION", "=" * 60)

    def session_end(self):
        """Log session end"""
        self._write_log("INFO", "SESSION", "Nova AI Agent session ended")
        self._write_log("INFO", "SESSION", "=" * 60)

    def log_conversation(self, user_input, nova_response):
        """Log conversation exchange"""
        self._write_log("INFO", "CONVERSATION", f"USER: {user_input}")
        self._write_log("INFO", "CONVERSATION", f"NOVA: {nova_response}")
        self._write_log("INFO", "CONVERSATION", "-" * 40)

    def log_api_call(self, model, status_code, response_preview, error=None):
        """Log API call details"""
        self._write_log("INFO", "API", f"Model: {model}, Status: {status_code}")
        if error:
            self._write_log("ERROR", "API", f"Error: {error}")
        else:
            preview = response_preview[:100] + "..." if len(response_preview) > 100 else response_preview
            self._write_log("DEBUG", "API", f"Response preview: {preview}")

    def log_tool_usage(self, tool_name, input_data, success=True):
        """Log tool usage"""
        status = "SUCCESS" if success else "FAILED"
        self._write_log("INFO", "TOOL", f"{tool_name} - {status}")
        if input_data:
            self._write_log("DEBUG", "TOOL", f"Input: {input_data}")

    def log_error(self, error_type, error_message, context=None):
        """Log errors"""
        self._write_log("ERROR", error_type, error_message)
        if context:
            self._write_log("ERROR", f"{error_type}_CONTEXT", str(context))

    def log_voice(self, action, text, success=True):
        """Log voice activities"""
        status = "SUCCESS" if success else "FAILED"
        self._write_log("INFO", "VOICE", f"{action} - {status}")
        if text:
            clean_text = text[:50] + "..." if len(text) > 50 else text
            self._write_log("DEBUG", "VOICE", f"Text: {clean_text}")

    def log_system(self, message):
        """Log system information"""
        self._write_log("INFO", "SYSTEM", message)

    def log_debug(self, category, message):
        """Log debug information"""
        self._write_log("DEBUG", category, message)

# Global logger instance
logger = NovaLogger()

# Convenience functions
def log_conversation(user_input, nova_response):
    logger.log_conversation(user_input, nova_response)

def log_api_call(model, status_code, response_preview, error=None):
    logger.log_api_call(model, status_code, response_preview, error)

def log_tool_usage(tool_name, input_data, success=True):
    logger.log_tool_usage(tool_name, input_data, success)

def log_error(error_type, error_message, context=None):
    logger.log_error(error_type, error_message, context)

def log_voice(action, text, success=True):
    logger.log_voice(action, text, success)

def log_system(message):
    logger.log_system(message)

def log_debug(category, message):
    logger.log_debug(category, message)
