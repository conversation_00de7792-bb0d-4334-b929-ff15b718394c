import json
import os

MEMORY_FILE = "nova_memory.json"

def load_memory():
    """Load Nova's conversation history"""
    if not os.path.exists(MEMORY_FILE):
        return [{"role": "system", "content": "You are <PERSON>, a warm female AI companion created by <PERSON>. Assistant responds kindly and conversationally"}]
    
    with open(MEMORY_FILE, 'r') as f:
        return json.load(f)

def save_memory(user_input, nova_response):
    """Save new conversation turn to memory"""
    memory = load_memory()
    
    # Add new conversation turn
    memory.append({"role": "user", "content": user_input})
    memory.append({"role": "assistant", "content": nova_response})
    
    # Trim memory to keep last 10 exchanges (12 messages)
    if len(memory) > 12:
        memory = memory[-12:]
    
    with open(MEMORY_FILE, 'w') as f:
        json.dump(memory, f, indent=2)

def clear_memory():
    """Reset <PERSON>'s memory"""
    with open(MEMORY_FILE, 'w') as f:
        json.dump([memory[0]], f)  # Keep system prompt
