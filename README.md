# 🌟 Nova AI Agent - Enhanced Edition

A powerful personal AI assistant powered by DeepSeek R1 model with comprehensive features for productivity, entertainment, and system control.

## ✨ Features

### 🧠 Core AI
- **DeepSeek R1 Model** - Advanced reasoning and conversation
- **Persistent Memory** - Remembers your conversations
- **Emotional Voice** - Dynamic speech with tone adaptation
- **Natural Language** - Conversational interface

### 🌤️ Weather & Time
- **Real-time Weather** - Current conditions and forecasts
- **World Clock** - Time zones and date information
- **Countdowns** - Time until events

### 📝 Productivity
- **Smart Reminders** - Set and manage reminders with notifications
- **Plan Management** - Save and organize your plans
- **Knowledge Base** - Personal information storage and retrieval
- **Web Search** - Latest information with auto-save

### 🔊 System Control
- **Volume Control** - Adjust, mute, unmute system audio
- **Application Management** - Open, close, list running apps
- **Screenshots** - Capture and save screen images
- **System Commands** - Lock, shutdown, restart computer

### 📱 Messaging Integration
- **Telegram Bot** - Full chat functionality via Telegram
- **WhatsApp Sender** - Send messages and notifications
- **Multi-platform** - Desktop, mobile, and web access

## 🚀 Quick Start

### Option 1: Easy Launcher (Recommended)
```bash
python nova_launcher.py
```
Choose from multiple modes:
- 🖥️ Desktop Mode (Voice + Text)
- 📱 Telegram Bot
- 💬 WhatsApp Sender
- 💻 Console Mode (Text only)

### Option 2: Direct Launch
```bash
# Desktop mode with voice
python main.py

# Telegram bot
python telegram_bot.py

# WhatsApp sender
python whatsapp_simple.py
```

## 📋 Installation

1. **Clone or download** this repository
2. **Install dependencies**:
   ```bash
   pip install -r requirments.txt
   ```
3. **Configure API keys** in `config/api_keys.py`
4. **Run Nova** using the launcher or directly

## 🔧 Configuration

### Required API Keys
- **OpenRouter** - For DeepSeek R1 model (get from https://openrouter.ai)
- **OpenWeather** - For weather features (free from https://openweathermap.org)
- **Telegram Bot** - For Telegram integration (from @BotFather)

### Settings
Edit `config/nova_config.py` to customize:
- Assistant name and personality
- Voice settings and speed
- Default locations and preferences
- Feature toggles

## 💬 Usage Examples

### Weather
```
You: What's the weather like?
Nova: ☀️ Clear Sky in London - 15°C, perfect weather! 🌟

You: Will it rain tomorrow?
Nova: 🌦️ Tomorrow's forecast shows light rain in the afternoon...
```

### Reminders
```
You: Remind me to call mom at 3pm
Nova: ✅ Reminder set! I'll remind you to call mom at 3:00 PM 🔔

You: List my reminders
Nova: 📝 Your Reminders:
1. Call mom - Today at 3:00 PM
```

### System Control
```
You: Set volume to 50
Nova: 🔊 Volume set to 50% - Perfect level! 🎵

You: Open calculator
Nova: 🚀 Calculator launched! Ready for some math? ✨
```

### Knowledge Base
```
You: Remember this: My favorite coffee shop is Blue Bottle on Main Street
Nova: ✅ Knowledge saved! I'll remember that for you 🧠

You: What do you know about coffee?
Nova: 🧠 I found this about coffee:
Your favorite coffee shop is Blue Bottle on Main Street...
```

## 📱 Platform Support

### Desktop (Windows)
- Full voice interaction
- System control features
- All productivity tools
- Local knowledge base

### Telegram
- Text-based chat
- All AI features
- Cross-platform access
- Mobile-friendly

### WhatsApp
- Message sending
- Notifications
- Simple integration
- Wide compatibility

## 🛠️ Advanced Features

### Voice Customization
- Multiple voice options
- Speed and pitch control
- Emotional tone adaptation
- Console mode for text-only

### Knowledge Management
- SQLite database storage
- Web scraping integration
- Smart search and retrieval
- Automatic categorization

### Reminder System
- Background monitoring
- Desktop notifications
- Flexible time parsing
- Persistent storage

### Windows Integration
- Volume control via Windows API
- Application management
- Screenshot capabilities
- System commands

## 🔒 Security & Privacy

- **Local Storage** - All data stored on your device
- **API Security** - Secure key management
- **No Data Collection** - Your conversations stay private
- **Optional Features** - Disable any features you don't need

## 🚨 Important Notes

### WhatsApp Integration
- Uses unofficial methods for full bot functionality
- May violate WhatsApp Terms of Service
- Simple sender mode is safer for notifications
- Consider using Telegram for full chat features

### System Control
- Some features require administrator privileges
- Windows-specific functionality
- Can be disabled in configuration

## 📖 Documentation

- **SETUP_GUIDE.md** - Detailed setup instructions
- **config/** - Configuration files and examples
- **core/** - Core functionality modules
- Inline code documentation

## 🤝 Contributing

This is a personal AI assistant project. Feel free to:
- Fork and customize for your needs
- Add new features and capabilities
- Share improvements and ideas
- Report issues and bugs

## 📄 License

This project is for personal use. Please respect API terms of service and usage limits.

## 🙏 Acknowledgments

- **DeepSeek** - For the amazing R1 model
- **OpenRouter** - For API access
- **OpenWeather** - For weather data
- **Telegram** - For bot platform
- **Python Community** - For excellent libraries

---

**Ready to enhance your productivity with Nova? Start with `python nova_launcher.py`! 🚀**
