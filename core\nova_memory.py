# nova_memory.py
import json
import os
from config import nova_config as conf

def load_memory():
    system_prompt = (
        f"You are {conf.NOVA_NAME}, a warm {conf.USER_NAME}'s female AI companion. "
        "You're empathetic, conversational, and express emotions naturally using emojis. "
        "You remember previous chats with <PERSON> and show genuine care for him. "
        f"Current context: {get_current_context()}"
    )
    
    if not os.path.exists(conf.MEMORY_FILE):
        return [{"role": "system", "content": system_prompt}]
    
    with open(conf.MEMORY_FILE, 'r') as f:
        memory = json.load(f)
        # Prepend system prompt to context
        if memory and memory[0]["role"] != "system":
            memory.insert(0, {"role": "system", "content": system_prompt})
        return memory

def save_memory(user_input, nova_response):
    memory = load_memory()
    
    # Add conversation turn
    memory.append({"role": "user", "content": user_input})
    memory.append({"role": "assistant", "content": nova_response})
    
    # Keep system message + last conversations
    if len(memory) > 1 + conf.MAX_HISTORY * 2:  # accounting for pairs
        memory = memory[:1] + memory[-(conf.MAX_HISTORY * 2):]
    
    with open(conf.MEMORY_FILE, 'w') as f:
        json.dump(memory, f, indent=2)

def clear_memory():
    """Reset conversation history except system prompt"""
    with open(conf.MEMORY_FILE, 'w') as f:
        json.dump(load_memory()[:1], f)  # Keep only system message

def get_current_context():
    """Provide current context for the system prompt"""
    from utils.helper_functions import get_formatted_time
    return f"It's currently {get_formatted_time()}. Engage naturally and respond conversationally."
