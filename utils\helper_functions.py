# helper_functions.py
from datetime import datetime

def truncate_string(text, max_length, suffix="..."):
    """Truncate text to specified length with suffix if needed"""
    if len(text) > max_length:
        return text[:max_length - len(suffix)] + suffix
    return text

def format_source(domain):
    """Format domain names for user presentation"""
    domain = domain.replace("www.", "")
    return domain.split(".")[0].title() if "." in domain else domain

def get_formatted_time():
    """Return properly formatted time for time-sensitive responses"""
    now = datetime.now()
    hour_name = "morning" if 5 <= now.hour < 12 else "afternoon" if 12 <= now.hour < 18 else "evening"
    return f"{hour_name} of {now.strftime('%B %d, %Y')} at {now.strftime('%I:%M %p')}"
