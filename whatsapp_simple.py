# whatsapp_simple.py
"""
Simple WhatsApp Integration using pywhatkit

This is a safer alternative that only sends messages, doesn't read them.
It opens WhatsApp Web and sends scheduled messages.

FEATURES:
- Send scheduled messages
- Send instant messages (opens browser)
- Send to groups
- Send images with captions

LIMITATIONS:
- Cannot read incoming messages
- Requires manual browser interaction
- Not suitable for real-time chat bot

For full chat bot functionality, use telegram_bot.py instead.
"""

import pywhatkit as pwk
import time
from datetime import datetime, timedelta
from core import nova_tools, nova_memory
from config.nova_config import NOVA_NAME

class SimpleWhatsAppSender:
    def __init__(self):
        self.name = NOVA_NAME
        
    def send_instant_message(self, phone_number, message):
        """Send an instant message (opens browser immediately)"""
        try:
            print(f"📱 Sending message to {phone_number}...")
            print(f"💬 Message: {message}")
            
            # Send message instantly (opens browser)
            pwk.sendwhatmsg_instantly(phone_number, message, wait_time=15, tab_close=True)
            
            print("✅ Message sent successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Failed to send message: {e}")
            return False
    
    def send_scheduled_message(self, phone_number, message, hour, minute):
        """Send a scheduled message"""
        try:
            print(f"⏰ Scheduling message to {phone_number} at {hour:02d}:{minute:02d}")
            print(f"💬 Message: {message}")
            
            pwk.sendwhatmsg(phone_number, message, hour, minute, wait_time=15, tab_close=True)
            
            print("✅ Message scheduled successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Failed to schedule message: {e}")
            return False
    
    def send_to_group(self, group_id, message):
        """Send message to a WhatsApp group"""
        try:
            print(f"👥 Sending message to group {group_id}...")
            print(f"💬 Message: {message}")
            
            pwk.sendwhatmsg_to_group_instantly(group_id, message, wait_time=15, tab_close=True)
            
            print("✅ Group message sent successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Failed to send group message: {e}")
            return False
    
    def send_image(self, phone_number, image_path, caption=""):
        """Send an image with optional caption"""
        try:
            print(f"🖼️ Sending image to {phone_number}...")
            
            if caption:
                caption = f"{caption}\n\n- {self.name} 🤖"
            
            pwk.sendwhats_image(phone_number, image_path, caption, wait_time=15, tab_close=True)
            
            print("✅ Image sent successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Failed to send image: {e}")
            return False
    
    def process_and_send(self, phone_number, user_message):
        """Process user message and send Nova's response"""
        try:
            print(f"🧠 Processing message: {user_message}")
            
            # Check if it's a tool request
            tool_response = nova_tools.process_tool_request(user_message)
            
            if tool_response:
                nova_reply = tool_response
            else:
                # Regular conversation
                memory = nova_memory.load_memory()
                
                # Customize for WhatsApp
                if memory and memory[0]["role"] == "system":
                    memory[0]["content"] = f"You are {NOVA_NAME}, a helpful AI assistant. Keep responses concise for WhatsApp mobile chat. Use emojis naturally."
                
                memory.append({"role": "user", "content": user_message})
                nova_reply = nova_tools.get_chat_response(memory)
                
                # Save to memory
                nova_memory.save_memory(user_message, nova_reply)
            
            # Add signature
            nova_reply += f"\n\n- {self.name} 🤖"
            
            # Send response
            return self.send_instant_message(phone_number, nova_reply)
            
        except Exception as e:
            print(f"❌ Error processing message: {e}")
            return False

def interactive_mode():
    """Interactive mode for sending messages"""
    sender = SimpleWhatsAppSender()
    
    print(f"📱 {NOVA_NAME} WhatsApp Sender")
    print("=" * 40)
    print("Commands:")
    print("1. send - Send instant message")
    print("2. schedule - Schedule message")
    print("3. group - Send to group")
    print("4. image - Send image")
    print("5. chat - Chat with Nova and send response")
    print("6. quit - Exit")
    print()
    
    while True:
        try:
            command = input("Enter command: ").strip().lower()
            
            if command == "quit":
                break
                
            elif command == "send":
                phone = input("Phone number (with country code, e.g., +1234567890): ")
                message = input("Message: ")
                sender.send_instant_message(phone, message)
                
            elif command == "schedule":
                phone = input("Phone number (with country code): ")
                message = input("Message: ")
                hour = int(input("Hour (24-hour format): "))
                minute = int(input("Minute: "))
                sender.send_scheduled_message(phone, message, hour, minute)
                
            elif command == "group":
                group_id = input("Group ID (get from WhatsApp Web URL): ")
                message = input("Message: ")
                sender.send_to_group(group_id, message)
                
            elif command == "image":
                phone = input("Phone number (with country code): ")
                image_path = input("Image path: ")
                caption = input("Caption (optional): ")
                sender.send_image(phone, image_path, caption)
                
            elif command == "chat":
                phone = input("Phone number (with country code): ")
                user_message = input("Your message to Nova: ")
                sender.process_and_send(phone, user_message)
                
            else:
                print("Unknown command. Try again.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def send_weather_update(phone_number, city=None):
    """Example: Send weather update"""
    sender = SimpleWhatsAppSender()
    
    try:
        from core.nova_weather import get_weather
        weather_info = get_weather(city)
        
        message = f"🌤️ Weather Update\n\n{weather_info}\n\nHave a great day! ☀️"
        return sender.send_instant_message(phone_number, message)
        
    except Exception as e:
        print(f"❌ Failed to send weather update: {e}")
        return False

def send_reminder_notification(phone_number, reminder_text):
    """Example: Send reminder notification"""
    sender = SimpleWhatsAppSender()
    
    message = f"🔔 Reminder Alert!\n\n{reminder_text}\n\nDon't forget! ⏰"
    return sender.send_instant_message(phone_number, message)

def main():
    """Main function"""
    print(f"📱 {NOVA_NAME} WhatsApp Integration")
    print("=" * 50)
    print()
    print("⚠️  IMPORTANT NOTES:")
    print("• This only SENDS messages, doesn't read them")
    print("• Browser will open for each message")
    print("• For full chat bot, use telegram_bot.py instead")
    print("• Make sure WhatsApp Web is logged in")
    print()
    
    print("Options:")
    print("1. Interactive mode - Send messages manually")
    print("2. Send weather update")
    print("3. Send reminder")
    print("4. Exit")
    print()
    
    choice = input("Choose option (1-4): ").strip()
    
    if choice == "1":
        interactive_mode()
        
    elif choice == "2":
        phone = input("Phone number (with country code): ")
        city = input("City (optional): ") or None
        send_weather_update(phone, city)
        
    elif choice == "3":
        phone = input("Phone number (with country code): ")
        reminder = input("Reminder text: ")
        send_reminder_notification(phone, reminder)
        
    elif choice == "4":
        print("👋 Goodbye!")
        
    else:
        print("Invalid choice!")

if __name__ == "__main__":
    main()
