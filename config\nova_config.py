# nova_config.py
# --- Basic Settings ---
NOVA_NAME = "Nova"
USER_NAME = "Hector"
MEMORY_FILE = "nova_memory.json"
MAX_HISTORY = 6

# --- API Settings ---
OPENROUTER_URL = "https://openrouter.ai/api/v1/chat/completions"
DEEPSEEK_MODEL = "Deepseek R1"
APP_NAME = "Nova AI Companion"
WEBSITE_URL = "http://localhost"

# --- Voice Settings ---
DEFAULT_VOICE = "Zira"  # Windows: "<PERSON>ira", Mac: "<PERSON>", Linux: "english-us"
VOICE_RATE = 165        # Words per minute
VOICE_PITCH = 107       # 103-112 recommended range
VOICE_VOLUME = 0.95

# --- Web Search Settings ---
MAX_SEARCH_RESULTS = 4
CONVO_TRUNCATE_LENGTH = 160  # Characters to show in summaries
