# nova_config.py
# --- Basic Settings ---
NOVA_NAME = "Nova"
USER_NAME = "Hector"
MEMORY_FILE = "nova_memory.json"
KNOWLEDGE_BASE_FILE = "nova_knowledge.db"
REMINDERS_FILE = "nova_reminders.json"
MAX_HISTORY = 6

# --- API Settings ---
OPENROUTER_URL = "https://openrouter.ai/api/v1/chat/completions"
DEEPSEEK_MODEL = "deepseek/deepseek-r1"
APP_NAME = "Nova AI Companion"
WEBSITE_URL = "http://localhost"

# --- Voice Settings ---
DEFAULT_VOICE = "Zira"  # Windows: "Zira", Mac: "Karen", Linux: "english-us"
VOICE_RATE = 165        # Words per minute
VOICE_PITCH = 107       # 103-112 recommended range
VOICE_VOLUME = 0.95

# --- Web Search Settings ---
MAX_SEARCH_RESULTS = 4
CONVO_TRUNCATE_LENGTH = 160  # Characters to show in summaries

# --- Weather Settings ---
WEATHER_UNITS = "metric"  # metric, imperial, or kelvin
DEFAULT_CITY = "London"   # Default city for weather queries

# --- Reminder Settings ---
REMINDER_CHECK_INTERVAL = 60  # seconds
NOTIFICATION_DURATION = 5     # seconds

# --- Knowledge Base Settings ---
MAX_KNOWLEDGE_ENTRIES = 1000
KNOWLEDGE_SIMILARITY_THRESHOLD = 0.7

# --- Windows Control Settings ---
VOLUME_STEP = 10  # Volume change step percentage
SCREENSHOT_PATH = "screenshots/"

# --- Messaging Settings ---
TELEGRAM_ENABLED = True
WHATSAPP_ENABLED = False  # Set to True when configured
