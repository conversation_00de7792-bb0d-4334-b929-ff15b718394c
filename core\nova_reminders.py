# nova_reminders.py
import json
import os
import threading
import time
from datetime import datetime, timedelta
from dateutil import parser
import uuid
from plyer import notification
from config import nova_config as conf

class ReminderService:
    def __init__(self):
        self.reminders_file = conf.REMINDERS_FILE
        self.reminders = self.load_reminders()
        self.running = False
        self.check_thread = None
        
    def load_reminders(self):
        """Load reminders from file"""
        if not os.path.exists(self.reminders_file):
            return []
        
        try:
            with open(self.reminders_file, 'r') as f:
                return json.load(f)
        except Exception:
            return []
    
    def save_reminders(self):
        """Save reminders to file"""
        try:
            with open(self.reminders_file, 'w') as f:
                json.dump(self.reminders, f, indent=2, default=str)
        except Exception as e:
            print(f"Error saving reminders: {e}")
    
    def add_reminder(self, message, when, reminder_type="reminder"):
        """Add a new reminder"""
        try:
            # Parse the time
            if isinstance(when, str):
                target_time = parser.parse(when)
            else:
                target_time = when
            
            # If time is in the past today, assume tomorrow
            now = datetime.now()
            if target_time.date() == now.date() and target_time.time() < now.time():
                target_time = target_time + timedelta(days=1)
            
            reminder = {
                'id': str(uuid.uuid4()),
                'message': message,
                'time': target_time.isoformat(),
                'type': reminder_type,
                'active': True,
                'created': now.isoformat()
            }
            
            self.reminders.append(reminder)
            self.save_reminders()
            
            # Start monitoring if not already running
            if not self.running:
                self.start_monitoring()
            
            time_str = target_time.strftime('%A, %B %d at %I:%M %p')
            return f"✅ **Reminder Set!**\n\n📝 **Message**: {message}\n⏰ **Time**: {time_str}\n\nI'll make sure to remind you! 🔔"
            
        except Exception as e:
            return f"❌ Sorry, I couldn't set that reminder: {str(e)}"
    
    def list_reminders(self):
        """List all active reminders"""
        active_reminders = [r for r in self.reminders if r['active']]
        
        if not active_reminders:
            return "📝 **No Active Reminders**\n\nYou're all caught up! Feel free to set new reminders anytime. ✨"
        
        response = "📝 **Your Active Reminders**\n\n"
        
        # Sort by time
        active_reminders.sort(key=lambda x: x['time'])
        
        for i, reminder in enumerate(active_reminders, 1):
            time_obj = datetime.fromisoformat(reminder['time'])
            time_str = time_obj.strftime('%A, %B %d at %I:%M %p')
            
            # Calculate time remaining
            now = datetime.now()
            diff = time_obj - now
            
            if diff.total_seconds() > 0:
                days = diff.days
                hours, remainder = divmod(diff.seconds, 3600)
                minutes, _ = divmod(remainder, 60)
                
                if days > 0:
                    remaining = f"in {days} day{'s' if days != 1 else ''}"
                elif hours > 0:
                    remaining = f"in {hours} hour{'s' if hours != 1 else ''}"
                elif minutes > 0:
                    remaining = f"in {minutes} minute{'s' if minutes != 1 else ''}"
                else:
                    remaining = "very soon"
            else:
                remaining = "overdue"
            
            response += f"**{i}.** {reminder['message']}\n"
            response += f"    ⏰ {time_str} ({remaining})\n"
            response += f"    🆔 ID: {reminder['id'][:8]}\n\n"
        
        response += "Use 'remove reminder [ID]' to delete a reminder! 🗑️"
        return response
    
    def remove_reminder(self, reminder_id):
        """Remove a reminder by ID (partial ID matching)"""
        for reminder in self.reminders:
            if reminder['id'].startswith(reminder_id) and reminder['active']:
                reminder['active'] = False
                self.save_reminders()
                return f"✅ **Reminder Removed**\n\n📝 Removed: {reminder['message']}\n\nAll done! 🎉"
        
        return f"❌ **Reminder Not Found**\n\nCouldn't find a reminder with ID starting with '{reminder_id}'. Use 'list reminders' to see all active reminders."
    
    def start_monitoring(self):
        """Start the reminder monitoring thread"""
        if not self.running:
            self.running = True
            self.check_thread = threading.Thread(target=self._monitor_reminders, daemon=True)
            self.check_thread.start()
    
    def stop_monitoring(self):
        """Stop the reminder monitoring"""
        self.running = False
        if self.check_thread:
            self.check_thread.join()
    
    def _monitor_reminders(self):
        """Monitor reminders and trigger notifications"""
        while self.running:
            try:
                now = datetime.now()
                
                for reminder in self.reminders:
                    if not reminder['active']:
                        continue
                    
                    reminder_time = datetime.fromisoformat(reminder['time'])
                    
                    # Check if reminder time has passed
                    if now >= reminder_time:
                        self._trigger_reminder(reminder)
                        reminder['active'] = False
                
                self.save_reminders()
                time.sleep(conf.REMINDER_CHECK_INTERVAL)
                
            except Exception as e:
                print(f"Error in reminder monitoring: {e}")
                time.sleep(conf.REMINDER_CHECK_INTERVAL)
    
    def _trigger_reminder(self, reminder):
        """Trigger a reminder notification"""
        try:
            # Desktop notification
            notification.notify(
                title=f"🔔 Nova Reminder",
                message=reminder['message'],
                timeout=conf.NOTIFICATION_DURATION,
                app_name="Nova AI"
            )
            
            # Console notification
            print(f"\n🔔 **REMINDER**: {reminder['message']}")
            print(f"⏰ Scheduled for: {reminder['time']}")
            print("=" * 50)
            
        except Exception as e:
            print(f"Error triggering reminder: {e}")
    
    def add_plan(self, plan_name, plan_details, due_date=None):
        """Add a plan (extended reminder with details)"""
        try:
            plan = {
                'id': str(uuid.uuid4()),
                'name': plan_name,
                'details': plan_details,
                'due_date': due_date.isoformat() if due_date else None,
                'type': 'plan',
                'active': True,
                'created': datetime.now().isoformat(),
                'completed': False
            }
            
            self.reminders.append(plan)
            self.save_reminders()
            
            response = f"📋 **Plan Saved!**\n\n📝 **Name**: {plan_name}\n📄 **Details**: {plan_details}"
            
            if due_date:
                due_str = due_date.strftime('%A, %B %d at %I:%M %p')
                response += f"\n⏰ **Due**: {due_str}"
            
            response += f"\n🆔 **ID**: {plan['id'][:8]}\n\nYour plan is safely stored! 💾"
            return response
            
        except Exception as e:
            return f"❌ Sorry, I couldn't save that plan: {str(e)}"
    
    def list_plans(self):
        """List all active plans"""
        plans = [r for r in self.reminders if r.get('type') == 'plan' and r['active']]
        
        if not plans:
            return "📋 **No Active Plans**\n\nReady to create some new plans? Just let me know! ✨"
        
        response = "📋 **Your Active Plans**\n\n"
        
        for i, plan in enumerate(plans, 1):
            response += f"**{i}. {plan['name']}**\n"
            response += f"    📄 {plan['details']}\n"
            
            if plan.get('due_date'):
                due_time = datetime.fromisoformat(plan['due_date'])
                due_str = due_time.strftime('%A, %B %d at %I:%M %p')
                response += f"    ⏰ Due: {due_str}\n"
            
            response += f"    🆔 ID: {plan['id'][:8]}\n\n"
        
        return response

# Global reminder service instance
reminder_service = ReminderService()

def add_reminder(message, when):
    """Add reminder - main function for Nova to use"""
    return reminder_service.add_reminder(message, when)

def list_reminders():
    """List reminders - main function for Nova to use"""
    return reminder_service.list_reminders()

def remove_reminder(reminder_id):
    """Remove reminder - main function for Nova to use"""
    return reminder_service.remove_reminder(reminder_id)

def add_plan(plan_name, plan_details, due_date=None):
    """Add plan - main function for Nova to use"""
    return reminder_service.add_plan(plan_name, plan_details, due_date)

def list_plans():
    """List plans - main function for Nova to use"""
    return reminder_service.list_plans()

# Start monitoring when module is imported
reminder_service.start_monitoring()
