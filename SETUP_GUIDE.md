# 🌟 Nova AI Agent - Complete Setup Guide

Welcome to your enhanced Nova AI agent! This guide will help you set up all the amazing new features.

## 🚀 Quick Start

1. **Install Dependencies**
   ```bash
   pip install -r requirments.txt
   ```

2. **Configure API Keys**
   - Edit `config/api_keys.py`
   - Add your OpenWeather API key (free from https://openweathermap.org/api)

3. **Run Nova**
   ```bash
   python main.py
   ```

## 🔧 Detailed Setup

### 1. Weather Service Setup
1. Go to https://openweathermap.org/api
2. Sign up for a free account
3. Get your API key
4. Edit `config/api_keys.py`:
   ```python
   OPENWEATHER_API_KEY = "your_api_key_here"
   ```

### 2. Telegram Bot Setup
1. Message @BotFather on Telegram
2. Create a new bot with `/newbot`
3. Get your bot token
4. Update `config/api_keys.py`:
   ```python
   TELEGRAM_BOT_TOKEN = "your_bot_token_here"
   ```
5. Run the bot:
   ```bash
   python telegram_bot.py
   ```

### 3. WhatsApp Integration
**Option 1: Simple Sender (Recommended)**
- Use `whatsapp_simple.py` for sending messages only
- Safer and more reliable
- Good for notifications and updates

**Option 2: Full Bot (Advanced)**
- Use `whatsapp_bot.py` for full chat functionality
- ⚠️ Uses unofficial methods - use at your own risk
- May violate WhatsApp Terms of Service

## 🎯 Features Overview

### 🌤️ Weather & Time
- **Weather**: "What's the weather like?", "Weather in London"
- **Forecast**: "Weather forecast", "Will it rain tomorrow?"
- **Time**: "What time is it?", "Current time"
- **Date**: "What's today's date?", "Date info"

### 📝 Reminders & Plans
- **Set Reminder**: "Remind me to call mom at 3pm"
- **List Reminders**: "Show my reminders", "List reminders"
- **Remove Reminder**: "Remove reminder [ID]"
- **Save Plan**: "Save plan: Weekend trip - book hotel and flights"
- **List Plans**: "Show my plans"

### 🔊 Windows Control
- **Volume**: "Set volume to 50", "Mute", "Unmute", "What's the volume?"
- **Applications**: "Open calculator", "Close chrome", "List running apps"
- **System**: "Take screenshot", "Lock computer"
- **Shutdown**: "Shutdown computer", "Restart computer"

### 🧠 Knowledge Base
- **Save Info**: "Remember this: Important meeting tomorrow"
- **Search**: "What do you know about Python?", "Search knowledge"
- **Web Scraping**: Automatically saves web search results
- **Recent**: "Show recent knowledge"

### 🌐 Web Search
- **Search**: "Search for latest news on AI", "Look up Python tutorials"
- **Auto-save**: Search results automatically saved to knowledge base

## 📱 Usage Examples

### Basic Conversation
```
You: Hello Nova
Nova: Hello Hector! I'm Nova, your personal companion. How can I make today special for you? 🌸

You: What's the weather like?
Nova: 🌍 Weather in London, UK
☀️ Clear Sky
🌡️ Temperature: 15°C (feels like 13°C)
💧 Humidity: 65%
💨 Wind: 3.2 m/s
Perfect weather for whatever you have planned! 🌟
```

### Setting Reminders
```
You: Remind me to call John at 3pm tomorrow
Nova: ✅ Reminder Set!
📝 Message: call John
⏰ Time: Tuesday, January 03 at 03:00 PM
I'll make sure to remind you! 🔔
```

### System Control
```
You: Set volume to 75
Nova: 🔊 Volume set to 75%
Perfect! Audio level adjusted. 🎵

You: Open calculator
Nova: 🚀 Calculator Launched!
Opening calculator for you now. ✨
```

### Knowledge Management
```
You: Remember this: My favorite restaurant is Mario's Italian on 5th Street
Nova: ✅ Knowledge Added!
📝 Topic: User Note
📚 Source: user
I've learned something new! 🧠✨

You: What do you know about restaurants?
Nova: 🧠 Knowledge Base Results for 'restaurants'
1. User Note
📄 My favorite restaurant is Mario's Italian on 5th Street
📚 Source: user
⏰ Added: 2024-01-02
```

## 🔧 Configuration

### Main Settings (`config/nova_config.py`)
- `NOVA_NAME`: AI assistant name
- `USER_NAME`: Your name
- `DEFAULT_CITY`: Default city for weather
- `VOICE_RATE`: Speech speed
- `MAX_SEARCH_RESULTS`: Web search results limit

### File Locations
- **Memory**: `nova_memory.json`
- **Knowledge Base**: `nova_knowledge.db`
- **Reminders**: `nova_reminders.json`
- **Screenshots**: `screenshots/`

## 🚨 Troubleshooting

### Common Issues

1. **Weather not working**
   - Check your OpenWeather API key
   - Ensure internet connection
   - Verify city name spelling

2. **Voice not working**
   - Check Windows speech settings
   - Ensure pyttsx3 is installed
   - Try different voice in config

3. **Reminders not triggering**
   - Keep Nova running in background
   - Check system notifications are enabled
   - Verify time format in reminders

4. **Windows control not working**
   - Run as administrator for some features
   - Check Windows permissions
   - Ensure required packages are installed

5. **Telegram bot not responding**
   - Verify bot token is correct
   - Check internet connection
   - Ensure bot is running

### Getting Help
- Check error messages in console
- Verify all dependencies are installed
- Ensure API keys are configured
- Try restarting Nova

## 🎉 Advanced Features

### Custom Commands
You can extend Nova by adding custom commands in `core/nova_tools.py`

### Voice Customization
Adjust voice settings in `config/nova_config.py`:
- `VOICE_RATE`: Speed (words per minute)
- `VOICE_PITCH`: Voice pitch
- `VOICE_VOLUME`: Volume level

### Knowledge Base Maintenance
- Automatic cleanup of old entries
- Configurable storage limits
- Smart deduplication

## 🔒 Security Notes

- Keep API keys secure and private
- Don't share your bot tokens
- Be cautious with WhatsApp automation
- Review permissions for system control features

## 🌟 Tips for Best Experience

1. **Speak naturally** - Nova understands conversational language
2. **Be specific** - "Set volume to 50" vs "change sound"
3. **Use reminders** - Great for staying organized
4. **Save knowledge** - Build your personal knowledge base
5. **Try different features** - Explore all capabilities

Enjoy your enhanced Nova AI agent! 🚀✨
