# main.py
import time
import os
import pyttsx3
from core.nova_voice import <PERSON><PERSON><PERSON>ce
from core.nova_chat import conversation_greeting, process_user_input
from config.nova_config import NOVA_NAME, USER_NAME

class DummyVoice:
    """Dummy voice class for console mode"""
    def say(self, text):
        pass  # Do nothing - console mode only

def main():
    # Check if running in console mode (no voice)
    console_mode = os.getenv('NOVA_CONSOLE_MODE') == '1'

    # Initialize voice system (or dummy for console mode)
    if console_mode:
        print("💻 Running in Console Mode (no voice)")
        tts_engine = DummyVoice()
    else:
        tts_engine = NovaVoice()

    conversation_state = "normal"

    # Check if it's first interaction
    first_interaction = True

    print(f"\n{'='*50}")
    print(f"✨ {NOVA_NAME} is waking up... powered by DeepSeek R1")
    if console_mode:
        print("💻 Console Mode - Text Only")
    else:
        print("🔊 Voice Mode - Text + Speech")
    print(f"{'='*50}\n")

    if first_interaction:
        conversation_greeting(tts_engine)
        first_interaction = False
    
    # Main conversation loop
    while True:
        try:
            user_input = input(f"\n{USER_NAME} 👉 ").strip()
            if not user_input:
                continue
            
            should_exit, conversation_state = process_user_input(
                user_input, 
                tts_engine
            )
            
            if should_exit:
                break
                
        except Exception as e:
            print(f"\n⚠️  System got confused: {e}")
            tts_engine.say("Sorry, I got a bit scrambled. Can we try that again?")
        
            conversation_state = "normal"

if __name__ == "__main__":
    main()
