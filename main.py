# main.py
import time
import pyttsx3
from core.nova_voice import <PERSON><PERSON><PERSON><PERSON>
from core.nova_chat import conversation_greeting, process_user_input
from config.nova_config import NOVA_NAME, USER_NAME

def main():
    # Initialize voice system
    tts_engine = NovaVoice()
    conversation_state = "normal"
    
    # Check if it's first interaction
    first_interaction = True
    
    print(f"\n{'='*50}")
    print(f"✨ {NOVA_NAME} is waking up... powered by DeepSeek R1")
    print(f"{'='*50}\n")
    
    if first_interaction:
        conversation_greeting(tts_engine)
        first_interaction = False
    
    # Main conversation loop
    while True:
        try:
            user_input = input(f"\n{USER_NAME} 👉 ").strip()
            if not user_input:
                continue
            
            should_exit, conversation_state = process_user_input(
                user_input, 
                tts_engine
            )
            
            if should_exit:
                break
                
        except Exception as e:
            print(f"\n⚠️  System got confused: {e}")
            tts_engine.say("Sorry, I got a bit scrambled. Can we try that again?")
        
            conversation_state = "normal"

if __name__ == "__main__":
    main()
