# nova_chat.py
import time
import threading
from core import nova_memory
from core import nova_tools
from core import nova_voice
from core.nova_logger import log_conversation, log_error, log_tool_usage, log_debug
from config import nova_config as conf

# Global state tracking
conversation_state = "normal"
current_search_results = []
current_web_query = ""

def conversation_greeting(tts_engine):
    """Initial Nova greeting"""
    greeting = (f"Hello {conf.USER_NAME}! I'm {conf.NOVA_NAME}, your personal companion. "
                "How can I make today special for you? 🌸")
    print(f"\n{conf.NOVA_NAME} ✨: {greeting}")
    tts_engine.say(greeting.replace('✨', ' '))
    

def process_user_input(user_input, tts_engine):
    """Main state machine for conversation handling"""
    global conversation_state, current_search_results, current_web_query
    
    # COMMAND HANDLING
    if user_input.lower() in ["exit", "quit", "bye"]:
        nova_memory.save_memory(user_input, "<exit command>")
        return handle_exit(tts_engine)
        
    elif user_input.lower() == "reset":
        nova_memory.clear_memory()
        print(f"\n{conf.NOVA_NAME} ✨: Memory cleared! It's like a brand new morning for us. 🌄")
        tts_engine.say("Memory cleared! It's like a brand new morning for us.")
        
        return False, conversation_state
    
    # STATE: Awaiting search query
    if conversation_state == "awaiting_query":
        current_web_query = user_input
        conversation_state = "searching"
        threading.Thread(target=perform_web_search_thread, args=(tts_engine,)).start()
        return False, conversation_state
    
    # STATE: Normal conversation handling
    if is_web_search_triggered(user_input):
        print(f"\n{conf.NOVA_NAME} ✨: I can look that up for you! "
              "What exactly would you like me to search for?")
        tts_engine.say("I can look that up for you! What would you like me to search?")
        
        conversation_state = "awaiting_query"
        return False, conversation_state
        
    return handle_normal_conversation(user_input, tts_engine)
    
def handle_normal_conversation(user_input, tts_engine):
    """Handle standard conversation flow"""
    log_debug("CHAT", f"Processing user input: {user_input}")

    # First, check if this is a tool request
    tool_response = nova_tools.process_tool_request(user_input)

    if tool_response:
        # This was a tool request, use the tool response
        nova_reply = tool_response
        log_tool_usage("AUTO_TOOL_DETECTION", user_input, True)
        log_debug("CHAT", f"Tool response: {tool_response[:100]}...")

        # Also save the web scraped content to knowledge base if it's a search
        if "🌐 I dug up some fresh info" in tool_response:
            try:
                from core.nova_knowledge import save_context
                save_context("recent_search", user_input)
            except Exception as e:
                log_error("KNOWLEDGE_SAVE", str(e), user_input)
    else:
        # Regular conversation - check knowledge base first
        log_debug("CHAT", "No tool detected, proceeding with regular conversation")
        try:
            from core.nova_knowledge import search_knowledge
            knowledge_result = search_knowledge(user_input)

            # If we found relevant knowledge, include it in the context
            if "No Knowledge Found" not in knowledge_result:
                log_debug("CHAT", "Found relevant knowledge, adding to context")
                # Load current conversation context
                memory = nova_memory.load_memory()

                # Add knowledge context
                knowledge_context = f"[KNOWLEDGE BASE CONTEXT]: {knowledge_result}"
                memory.append({"role": "system", "content": knowledge_context})
                memory.append({"role": "user", "content": user_input})

                # Get Nova's response with knowledge context
                nova_reply = nova_tools.get_chat_response(memory)
            else:
                # No relevant knowledge, proceed normally
                log_debug("CHAT", "No relevant knowledge found, proceeding normally")
                memory = nova_memory.load_memory()
                memory.append({"role": "user", "content": user_input})
                nova_reply = nova_tools.get_chat_response(memory)

        except Exception as e:
            # Fallback to normal conversation if knowledge base fails
            log_error("KNOWLEDGE_SEARCH", str(e), user_input)
            memory = nova_memory.load_memory()
            memory.append({"role": "user", "content": user_input})
            nova_reply = nova_tools.get_chat_response(memory)

    # Check if we got a valid response
    if not nova_reply or nova_reply.strip() == "":
        nova_reply = "I'm having trouble forming a response right now. Could you try asking again? 🤔"
        log_error("EMPTY_RESPONSE", "Got empty response from API", user_input)

    # Emotional voice modulation
    try:
        tone = nova_voice.detect_speech_tone(nova_reply)
        nova_voice.adjust_voice_for_tone(tts_engine, tone)
    except Exception as e:
        log_error("VOICE_MODULATION", str(e), nova_reply)

    # Output
    print(f"\n{conf.NOVA_NAME} ✨: {nova_reply}")
    try:
        tts_engine.say(nova_reply)
    except Exception as e:
        log_error("VOICE_OUTPUT", str(e), nova_reply)

    # Log the conversation
    log_conversation(user_input, nova_reply)

    # Save to memory
    try:
        nova_memory.save_memory(user_input, nova_reply)
    except Exception as e:
        log_error("MEMORY_SAVE", str(e), f"User: {user_input}, Nova: {nova_reply}")

    # Save important information to knowledge base
    try:
        from core.nova_knowledge import save_context
        save_context("conversation", f"User: {user_input} | Nova: {nova_reply}")
    except Exception as e:
        log_error("KNOWLEDGE_CONTEXT_SAVE", str(e), user_input)

    return False, "normal"

def perform_web_search_thread(tts_engine):
    """Run web search and present results"""
    global conversation_state, current_search_results, current_web_query
    
    # Searching indicator
    print(f"\n🔍 {conf.NOVA_NAME} is searching...")
    
    # Perform search
    search_results = nova_tools.perform_web_search(current_web_query)
    summary = nova_tools.create_conversational_summary(search_results, current_web_query)
    current_search_results = search_results
    
    # Emotional delivery
    tone = nova_voice.detect_speech_tone(summary)
    nova_voice.adjust_voice_for_tone(tts_engine, tone)
    
    # Present results
    print(f"\n{conf.NOVA_NAME} ✨: {summary}")
    tts_engine.say(summary)
    
    
    # Ask for next action
    if search_results:
        print(f"\n{conf.NOVA_NAME} ✨: Would you like me to open any specific page? Just say the number!")
        tts_engine.say("Would you like me to open any specific page? You can say the number.")
        
        conversation_state = "awaiting_followup"
    else:
        conversation_state = "normal"

def is_web_search_triggered(user_input):
    """Check if query needs web search"""
    triggers = [
        "current news", "recent update", "latest development", "happening now",
        "search web for", "look up", "find information about", "results for",
        "online about", "on the internet"
    ]
    return any(phrase in user_input.lower() for phrase in triggers)

def handle_exit(tts_engine):
    """Graceful exit sequence"""
    farewell = "Rest well, Hector. I'll be here whenever you need a friend. 🌙💤"
    print(f"\n{conf.NOVA_NAME} ✨: {farewell}")
    tts_engine.say(farewell)
    
    return True, "exit"
