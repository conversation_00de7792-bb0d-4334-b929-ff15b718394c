# nova_chat.py
import time
import threading
from core import nova_memory
from core import nova_tools
from core import nova_voice
from config import nova_config as conf

# Global state tracking
conversation_state = "normal"
current_search_results = []
current_web_query = ""

def conversation_greeting(tts_engine):
    """Initial Nova greeting"""
    greeting = (f"Hello {conf.USER_NAME}! I'm {conf.NOVA_NAME}, your personal companion. "
                "How can I make today special for you? 🌸")
    print(f"\n{conf.NOVA_NAME} ✨: {greeting}")
    tts_engine.say(greeting.replace('✨', ' '))
    

def process_user_input(user_input, tts_engine):
    """Main state machine for conversation handling"""
    global conversation_state, current_search_results, current_web_query
    
    # COMMAND HANDLING
    if user_input.lower() in ["exit", "quit", "bye"]:
        nova_memory.save_memory(user_input, "<exit command>")
        return handle_exit(tts_engine)
        
    elif user_input.lower() == "reset":
        nova_memory.clear_memory()
        print(f"\n{conf.NOVA_NAME} ✨: Memory cleared! It's like a brand new morning for us. 🌄")
        tts_engine.say("Memory cleared! It's like a brand new morning for us.")
        
        return False, conversation_state
    
    # STATE: Awaiting search query
    if conversation_state == "awaiting_query":
        current_web_query = user_input
        conversation_state = "searching"
        threading.Thread(target=perform_web_search_thread, args=(tts_engine,)).start()
        return False, conversation_state
    
    # STATE: Normal conversation handling
    if is_web_search_triggered(user_input):
        print(f"\n{conf.NOVA_NAME} ✨: I can look that up for you! "
              "What exactly would you like me to search for?")
        tts_engine.say("I can look that up for you! What would you like me to search?")
        
        conversation_state = "awaiting_query"
        return False, conversation_state
        
    return handle_normal_conversation(user_input, tts_engine)
    
def handle_normal_conversation(user_input, tts_engine):
    """Handle standard conversation flow"""
    # First, check if this is a tool request
    tool_response = nova_tools.process_tool_request(user_input)

    if tool_response:
        # This was a tool request, use the tool response
        nova_reply = tool_response

        # Also save the web scraped content to knowledge base if it's a search
        if "🌐 I dug up some fresh info" in tool_response:
            # Extract search results and save to knowledge base
            try:
                from core.nova_knowledge import save_context
                save_context("recent_search", user_input)
            except:
                pass
    else:
        # Regular conversation - check knowledge base first
        try:
            from core.nova_knowledge import search_knowledge
            knowledge_result = search_knowledge(user_input)

            # If we found relevant knowledge, include it in the context
            if "No Knowledge Found" not in knowledge_result:
                # Load current conversation context
                memory = nova_memory.load_memory()

                # Add knowledge context
                knowledge_context = f"[KNOWLEDGE BASE CONTEXT]: {knowledge_result}"
                memory.append({"role": "system", "content": knowledge_context})
                memory.append({"role": "user", "content": user_input})

                # Get Nova's response with knowledge context
                nova_reply = nova_tools.get_chat_response(memory)
            else:
                # No relevant knowledge, proceed normally
                memory = nova_memory.load_memory()
                memory.append({"role": "user", "content": user_input})
                nova_reply = nova_tools.get_chat_response(memory)

        except Exception as e:
            # Fallback to normal conversation if knowledge base fails
            memory = nova_memory.load_memory()
            memory.append({"role": "user", "content": user_input})
            nova_reply = nova_tools.get_chat_response(memory)

    # Emotional voice modulation
    tone = nova_voice.detect_speech_tone(nova_reply)
    nova_voice.adjust_voice_for_tone(tts_engine, tone)

    # Output
    print(f"\n{conf.NOVA_NAME} ✨: {nova_reply}")
    tts_engine.say(nova_reply)

    # Save to memory
    nova_memory.save_memory(user_input, nova_reply)

    # Save important information to knowledge base
    try:
        from core.nova_knowledge import save_context
        save_context("conversation", f"User: {user_input} | Nova: {nova_reply}")
    except:
        pass

    return False, "normal"

def perform_web_search_thread(tts_engine):
    """Run web search and present results"""
    global conversation_state, current_search_results, current_web_query
    
    # Searching indicator
    print(f"\n🔍 {conf.NOVA_NAME} is searching...")
    
    # Perform search
    search_results = nova_tools.perform_web_search(current_web_query)
    summary = nova_tools.create_conversational_summary(search_results, current_web_query)
    current_search_results = search_results
    
    # Emotional delivery
    tone = nova_voice.detect_speech_tone(summary)
    nova_voice.adjust_voice_for_tone(tts_engine, tone)
    
    # Present results
    print(f"\n{conf.NOVA_NAME} ✨: {summary}")
    tts_engine.say(summary)
    
    
    # Ask for next action
    if search_results:
        print(f"\n{conf.NOVA_NAME} ✨: Would you like me to open any specific page? Just say the number!")
        tts_engine.say("Would you like me to open any specific page? You can say the number.")
        
        conversation_state = "awaiting_followup"
    else:
        conversation_state = "normal"

def is_web_search_triggered(user_input):
    """Check if query needs web search"""
    triggers = [
        "current news", "recent update", "latest development", "happening now",
        "search web for", "look up", "find information about", "results for",
        "online about", "on the internet"
    ]
    return any(phrase in user_input.lower() for phrase in triggers)

def handle_exit(tts_engine):
    """Graceful exit sequence"""
    farewell = "Rest well, Hector. I'll be here whenever you need a friend. 🌙💤"
    print(f"\n{conf.NOVA_NAME} ✨: {farewell}")
    tts_engine.say(farewell)
    
    return True, "exit"
