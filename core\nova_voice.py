# core/nova_voice.py
import pyttsx3
from config import nova_config as conf

def detect_speech_tone(text):
    """Detect emotional tone from text (simplified)"""
    text_lower = text.lower()

    if any(word in text_lower for word in ['!', 'excited', 'amazing', 'wonderful', 'great']):
        return "excited"
    elif any(word in text_lower for word in ['sorry', 'sad', 'unfortunately', 'problem']):
        return "sad"
    elif any(word in text_lower for word in ['?', 'how', 'what', 'when', 'where', 'why']):
        return "curious"
    else:
        return "neutral"

def adjust_voice_for_tone(tts_engine, tone):
    """Adjust voice settings based on emotional tone"""
    if not hasattr(tts_engine, 'engine') or not tts_engine.engine:
        return

    try:
        voices = tts_engine.engine.getProperty('voices')
        if not voices:
            return

        # Adjust rate based on tone
        if tone == "excited":
            tts_engine.engine.setProperty('rate', conf.VOICE_RATE + 20)
        elif tone == "sad":
            tts_engine.engine.setProperty('rate', conf.VOICE_RATE - 20)
        elif tone == "curious":
            tts_engine.engine.setProperty('rate', conf.VOICE_RATE + 10)
        else:  # neutral
            tts_engine.engine.setProperty('rate', conf.VOICE_RATE)

        # Set volume
        tts_engine.engine.setProperty('volume', conf.VOICE_VOLUME)

    except Exception as e:
        print(f"Voice adjustment error: {e}")

class NovaVoice:
    def __init__(self):
        try:
            self.engine = pyttsx3.init()
            self._setup_voice()
        except Exception as e:
            print(f"Voice initialization error: {e}")
            self.engine = None

    def _setup_voice(self):
        """Setup voice properties"""
        if not self.engine:
            return

        try:
            # Set voice properties
            voices = self.engine.getProperty('voices')
            if voices:
                # Try to find the configured voice
                for voice in voices:
                    if conf.DEFAULT_VOICE.lower() in voice.name.lower():
                        self.engine.setProperty('voice', voice.id)
                        break
                else:
                    # Use first available voice
                    self.engine.setProperty('voice', voices[0].id)

            # Set rate, volume
            self.engine.setProperty('rate', conf.VOICE_RATE)
            self.engine.setProperty('volume', conf.VOICE_VOLUME)

        except Exception as e:
            print(f"Voice setup error: {e}")

    def say(self, text):
        """Speak the given text"""
        if self.engine and text.strip():
            try:
                # Clean text for speech (remove emojis)
                clean_text = self._clean_text_for_speech(text)
                self.engine.say(clean_text)
                self.engine.runAndWait()
            except Exception as e:
                print(f"Speech error: {e}")
                self.engine = None

    def _clean_text_for_speech(self, text):
        """Clean text for better speech synthesis"""
        import re

        # Remove emojis
        emoji_pattern = re.compile("["
                                 u"\U0001F600-\U0001F64F"  # emoticons
                                 u"\U0001F300-\U0001F5FF"  # symbols & pictographs
                                 u"\U0001F680-\U0001F6FF"  # transport & map symbols
                                 u"\U0001F1E0-\U0001F1FF"  # flags (iOS)
                                 u"\U00002702-\U000027B0"
                                 u"\U000024C2-\U0001F251"
                                 "]+", flags=re.UNICODE)

        clean_text = emoji_pattern.sub('', text)

        # Remove markdown formatting
        clean_text = re.sub(r'\*\*(.*?)\*\*', r'\1', clean_text)  # Bold
        clean_text = re.sub(r'\*(.*?)\*', r'\1', clean_text)      # Italic

        return clean_text.strip()