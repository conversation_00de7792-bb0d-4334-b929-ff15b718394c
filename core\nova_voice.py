# core/nova_voice.py (ultra minimal)

import pyttsx3

def detect_speech_tone(text):
        return "neutral"  # Simple fix to avoid errors


class NovaVoice:
    def __init__(self):
        try:
            self.engine = pyttsx3.init()
        except:
            self.engine = None
    
    def say(self, text):
        if self.engine and text.strip():
            try:
                self.engine.say(text)
                self.engine.runAndWait()
            except:
                self.engine = None 

    