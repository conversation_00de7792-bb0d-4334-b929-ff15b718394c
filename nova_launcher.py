# nova_launcher.py
"""
Nova AI Agent Launcher

This script provides different ways to run Nova:
1. Desktop Mode - Full voice and text interface
2. Telegram <PERSON>t - Chat via Telegram
3. WhatsApp Sender - Send messages via WhatsApp
4. Console Only - Text-only mode (no voice)
"""

import os
import sys
import subprocess
from config.nova_config import NOVA_NAME

def print_banner():
    """Print Nova banner"""
    print("=" * 60)
    print(f"🌟 {NOVA_NAME} AI Agent - Enhanced Edition")
    print("=" * 60)
    print("🧠 Powered by DeepSeek R1")
    print("🌤️ Weather • ⏰ Reminders • 🔊 System Control • 🧠 Knowledge Base")
    print("📱 Telegram • 💬 WhatsApp • 🌐 Web Search")
    print("=" * 60)
    print()

def check_dependencies():
    """Check if required packages are installed"""
    required_packages = [
        'requests', 'pyttsx3', 'duckduckgo-search', 'beautifulsoup4',
        'openweathermap', 'python-telegram-bot', 'psutil', 'plyer',
        'python-dateutil', 'pytz'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   • {package}")
        print()
        print("Please install missing packages:")
        print("pip install -r requirments.txt")
        print()
        return False
    
    return True

def check_api_keys():
    """Check if API keys are configured"""
    try:
        from config.api_keys import OPENROUTER_API_KEY, OPENWEATHER_API_KEY, TELEGRAM_BOT_TOKEN
        
        issues = []
        
        if not OPENROUTER_API_KEY or OPENROUTER_API_KEY == "YOUR_API_KEY_HERE":
            issues.append("OpenRouter API key (for DeepSeek R1)")
        
        if not OPENWEATHER_API_KEY or OPENWEATHER_API_KEY == "YOUR_OPENWEATHER_API_KEY_HERE":
            issues.append("OpenWeather API key (for weather)")
        
        if not TELEGRAM_BOT_TOKEN or TELEGRAM_BOT_TOKEN == "YOUR_TELEGRAM_BOT_TOKEN_HERE":
            issues.append("Telegram Bot token (for Telegram integration)")
        
        if issues:
            print("⚠️  API Key Configuration Issues:")
            for issue in issues:
                print(f"   • {issue}")
            print()
            print("Edit config/api_keys.py to configure API keys")
            print("See SETUP_GUIDE.md for detailed instructions")
            print()
            return False
        
        return True
        
    except ImportError:
        print("❌ Cannot import API keys configuration")
        return False

def run_desktop_mode():
    """Run Nova in desktop mode with voice"""
    print("🖥️ Starting Nova in Desktop Mode...")
    print("Features: Voice, Text, All Tools")
    print("Press Ctrl+C to stop")
    print()
    
    try:
        subprocess.run([sys.executable, "main.py"])
    except KeyboardInterrupt:
        print("\n👋 Nova Desktop Mode stopped")

def run_telegram_bot():
    """Run Nova as Telegram bot"""
    print("📱 Starting Nova Telegram Bot...")
    print("Features: Text chat via Telegram, All Tools")
    print("Press Ctrl+C to stop")
    print()
    
    try:
        subprocess.run([sys.executable, "telegram_bot.py"])
    except KeyboardInterrupt:
        print("\n👋 Nova Telegram Bot stopped")

def run_whatsapp_sender():
    """Run WhatsApp sender"""
    print("💬 Starting WhatsApp Sender...")
    print("Features: Send messages via WhatsApp")
    print()
    
    try:
        subprocess.run([sys.executable, "whatsapp_simple.py"])
    except KeyboardInterrupt:
        print("\n👋 WhatsApp Sender stopped")

def run_console_mode():
    """Run Nova in console-only mode"""
    print("💻 Starting Nova in Console Mode...")
    print("Features: Text only (no voice), All Tools")
    print("Press Ctrl+C to stop")
    print()
    
    # Temporarily disable voice for console mode
    os.environ['NOVA_CONSOLE_MODE'] = '1'
    
    try:
        subprocess.run([sys.executable, "main.py"])
    except KeyboardInterrupt:
        print("\n👋 Nova Console Mode stopped")

def show_system_info():
    """Show system information and feature status"""
    print("🔧 System Information")
    print("-" * 30)
    
    # Check Python version
    print(f"Python Version: {sys.version.split()[0]}")
    
    # Check OS
    import platform
    print(f"Operating System: {platform.system()} {platform.release()}")
    
    # Check features
    print("\n🎯 Feature Status")
    print("-" * 20)
    
    # Voice
    try:
        import pyttsx3
        print("✅ Voice (pyttsx3)")
    except ImportError:
        print("❌ Voice (pyttsx3 not installed)")
    
    # Weather
    try:
        from config.api_keys import OPENWEATHER_API_KEY
        if OPENWEATHER_API_KEY != "YOUR_OPENWEATHER_API_KEY_HERE":
            print("✅ Weather Service")
        else:
            print("⚠️  Weather Service (API key needed)")
    except:
        print("❌ Weather Service (configuration error)")
    
    # Telegram
    try:
        from config.api_keys import TELEGRAM_BOT_TOKEN
        import telegram
        if TELEGRAM_BOT_TOKEN != "YOUR_TELEGRAM_BOT_TOKEN_HERE":
            print("✅ Telegram Bot")
        else:
            print("⚠️  Telegram Bot (token needed)")
    except ImportError:
        print("❌ Telegram Bot (python-telegram-bot not installed)")
    except:
        print("⚠️  Telegram Bot (configuration error)")
    
    # Windows Control
    try:
        import psutil
        import pyautogui
        print("✅ Windows Control")
    except ImportError:
        print("❌ Windows Control (missing packages)")
    
    # Knowledge Base
    try:
        import sqlite3
        print("✅ Knowledge Base")
    except ImportError:
        print("❌ Knowledge Base (sqlite3 not available)")
    
    print()

def main():
    """Main launcher function"""
    print_banner()
    
    # Check system requirements
    if not check_dependencies():
        input("Press Enter to continue anyway or Ctrl+C to exit...")
        print()
    
    # Check API keys (warning only)
    check_api_keys()
    
    while True:
        print("🚀 Choose how to run Nova:")
        print()
        print("1. 🖥️  Desktop Mode (Voice + Text + All Features)")
        print("2. 📱 Telegram Bot (Chat via Telegram)")
        print("3. 💬 WhatsApp Sender (Send messages only)")
        print("4. 💻 Console Mode (Text only, no voice)")
        print("5. 🔧 System Information")
        print("6. 📖 Open Setup Guide")
        print("7. ❌ Exit")
        print()
        
        try:
            choice = input("Enter your choice (1-7): ").strip()
            
            if choice == "1":
                run_desktop_mode()
            elif choice == "2":
                run_telegram_bot()
            elif choice == "3":
                run_whatsapp_sender()
            elif choice == "4":
                run_console_mode()
            elif choice == "5":
                show_system_info()
                input("\nPress Enter to continue...")
            elif choice == "6":
                if os.path.exists("SETUP_GUIDE.md"):
                    if sys.platform.startswith('win'):
                        os.startfile("SETUP_GUIDE.md")
                    else:
                        subprocess.run(["open" if sys.platform == "darwin" else "xdg-open", "SETUP_GUIDE.md"])
                else:
                    print("Setup guide not found!")
            elif choice == "7":
                print("👋 Goodbye! Thanks for using Nova!")
                break
            else:
                print("❌ Invalid choice. Please try again.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print()

if __name__ == "__main__":
    main()
